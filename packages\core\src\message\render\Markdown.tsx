import React from "react";
import Markdown from "react-markdown";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import remarkGfm from "remark-gfm";
import remarkMath from "remark-math";

import CodeViewer from "./CoderViewer";
import Math from "./Math";
import MermaidRenderer from "./Mermaid";

interface MarkdownRenderProps {
  content: string;
}

const MarkdownContext = React.createContext<{
  content: string;
}>({
  content: "",
});

const Code: React.FC<any> = (props) => {
  const { className, children, ...rest } = props;
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "";
  const excludes = ["mermaid", "math"];

  return (
    <>
      {language === "mermaid" && <MermaidRenderer code={children} />}
      {language === "math" && <Math className={className} content={children} />}
      {!excludes.includes(language) && (
        <CodeViewer code={children} language={language} className={className} {...rest}>
          {children}
        </CodeViewer>
      )}
    </>
  );
};

const components = {
  code: Code,
};
const rehypePlugins = [rehypeHighlight, rehypeRaw];
const remarkPlugins = [remarkGfm, remarkMath];

// TODO
// function normalizing(message: string) {
//   return message.replace(/\\/g, "\\");
// }

const MarkdownRender: React.FC<MarkdownRenderProps> = (props) => {
  const { content } = props;

  return (
    <div className="markdown-body">
      <MarkdownContext.Provider value={{ content }}>
        <Markdown rehypePlugins={rehypePlugins} remarkPlugins={remarkPlugins} components={components}>
          {content}
        </Markdown>
      </MarkdownContext.Provider>
    </div>
  );
};

export default MarkdownRender;
