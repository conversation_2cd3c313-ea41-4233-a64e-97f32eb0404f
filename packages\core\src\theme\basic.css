:root {
  --agent-color-blue: #0b68e6;
  --agent-color-red: #FA5271;
  --agent-color-black-85: rgba(37, 45, 62, 0.85);
  --agent-color-black-65: rgba(37, 45, 62, 0.65);
  interpolate-size: allow-keywords; 
}

@utility text-primary {
  color: var(--agent-color-blue);
}

@utility text-danger {
  color: var(--agent-color-red);
}

@utility text-black-85 {
  color: var(--agent-color-black-85);
}

@utility text-black-65 {
  color: var(--agent-color-black-65);
}

@utility text-black-45 {
  color: rgba(37, 45, 62, 0.45);
}

@utility text-black-25 {
  color: rgba(37, 45, 62, 0.25);
}

@utility shadow-box {
  box-shadow:
    0px 9px 28px 0px rgba(0, 0, 0, 0.05),
    0px 6px 16px -8px rgba(0, 0, 0, 0.08);
}

@utility circle-loader {
  width: 32px;
  height: 32px;
  border: 4px solid #fff;
  border-bottom-color: #0b68e6;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  opacity: 0.52;
  margin: 0 auto;
}

@utility message-loader {
  width: 30px;
  aspect-ratio: 2;
  --_g: no-repeat radial-gradient(circle closest-side, rgba(11, 104, 230, 1) 50%, #0000);
  background:
    var(--_g) 0% 50%,
    var(--_g) 50% 50%,
    var(--_g) 100% 50%;
  background-size: calc(100% / 3) 50%;
  animation: l3 1s infinite linear;
}

@keyframes l3 {
  20% {
    background-position:
      0% 0%,
      50% 50%,
      100% 50%;
  }
  40% {
    background-position:
      0% 100%,
      50% 0%,
      100% 50%;
  }
  60% {
    background-position:
      0% 50%,
      50% 100%,
      100% 0%;
  }
  80% {
    background-position:
      0% 50%,
      50% 50%,
      100% 100%;
  }
}