import { atom, useAtom } from "jotai";
import { nanoid } from "nanoid";
import { useContext } from "react";

import { IConversation } from "@/types";
import { IMessagePackage, MessagePackageStatus, MessagePackageType } from "@/types";

import { Message, createHumanMessage } from "../common/message";
import { AgentChatContext } from "./context";

export const loginState = atom(false);

export const useLoginState = () => {
  return useAtom(loginState);
};

export class AgentStore {
  public messages = atom<Message[]>([]);
  public conversations = atom<IConversation[]>([]);
  // 当前激活的智能体
  public activeAgentCode = atom<string | null>(null);
  // 当前激活的智能体菜单项
  public activeAgentMenuCode = atom<string | null>(null);
  public activeConversation = atom<IConversation | null>(null);
  public activeConversationId = atom<string | null>(null);
  // 是否正在加载AI消息
  public isLoadingMessage = atom(false);
}

export const useMessages = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.messages);
};

export const useActiveAgentCode = () => {
  const store = useContext(AgentChatContext).store;
  return useAtom(store.activeAgentCode);
};

export const useActiveAgentMenuCode = () => {
  const store = useContext(AgentChatContext).store;
  return useAtom(store.activeAgentMenuCode);
};

export const useHumanMessage = () => {
  const [, setMessages] = useMessages();
  const [activeAgentCode] = useActiveAgentCode();

  const create = (content: string) => {
    // 创建消息包
    const msgPackage: IMessagePackage = {
      package_id: 0,
      package_type: MessagePackageType.Text,
      status: MessagePackageStatus.Finished,
      data: content,
    };

    // 创建消息
    if (!activeAgentCode) {
      console.error("activeAgentCode is null");
      return;
    }
    const message = createHumanMessage(nanoid(), [msgPackage], activeAgentCode);
    if (!message) return null;
    setMessages((messages) => [...messages, message]);

    return message;
  };

  return { create };
};

export const useActiveConversation = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.activeConversation);
};

export const useActiveConversationId = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.activeConversationId);
};

export const useConversations = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.conversations);
};

export const useIsLoadingMessage = () => {
  const store = useContext(AgentChatContext).store;

  return useAtom(store.isLoadingMessage);
};
