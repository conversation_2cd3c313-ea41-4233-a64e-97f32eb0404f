import React from "react";
import { BlockMath, InlineMath } from "react-katex";

const Math: React.FC<{ className?: string; content: string }> = (props) => {
  const { className, content } = props;
  const isInline = className?.includes("inline");
  let math = content.replace(/&#92;/g, "\\");
  math = math.replace(/^```math\n/, "").replace(/\n```$/, "");

  return isInline ? <InlineMath math={math} /> : <BlockMath math={math} />;
};

export default Math;
