import { Button, Input, Modal, Select, Spin, message } from "antd";
import dayjs from "dayjs";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";

import { RedoOutlined } from "@ant-design/icons";
import { del, get, put, useAgentConfigs, useNavigate } from "@cscs-agent/core";
import type { ConversationData, ConversationHistoryResponse } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import AgentIcon from "./AgentIcon";
import Content from "./Content";
import Empty from "./Empty";
import NavigationBar from "./NaviagtionBar";
import Title from "./Title";

type ConversationItem = ConversationData & { deleted?: boolean; pending?: boolean };

const ConversationSearch: React.FC = () => {
  const [inputValue, setInputValue] = useState("");
  const [keyword, setKeyword] = useState("");
  const [conversations, setConversations] = useState<ConversationItem[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const agents = useAgentConfigs();
  const [agentCode, setAgentCode] = useState("");
  const [currentEditId, setCurrentEditId] = useState<string | null>(null);
  const size = 20;
  const [searchOnChange, setSearchOnChange] = useState(false);

  const agentOptions = useMemo(() => {
    const options = agents.map((i) => {
      return {
        label: i.name,
        value: i.code,
      };
    });
    options.unshift({
      label: "全部智能体",
      value: "",
    });
    return options;
  }, [agents]);

  useEffect(() => {
    handleSearch("", "", 1);
  }, []);

  const rename = useCallback(async (id: string, title: string) => {
    setCurrentEditId(null);

    put(`/conversation/${id}`, { title }).then(() => {
      setConversations((conversations) => {
        const conversation = conversations.find((i) => i.id === id);
        if (conversation) {
          conversation.title = title;
        }
        return [...conversations];
      });
    });
  }, []);

  const handleSearch = ($keyword: string, _agentCode: string, currentPage?: number) => {
    setKeyword($keyword);

    const nextPage = currentPage ? currentPage : page + 1;
    setPage(nextPage);
    if (nextPage === 1) {
      setConversations([]);
    }
    setLoading(true);
    get<ConversationHistoryResponse>(`/conversation/search`, {
      keyword: $keyword,
      agent_code: _agentCode,
      page: nextPage,
      size,
    })
      .then((res) => {
        setConversations((prev) => [...prev, ...res.data.data]);
        setHasMore(res.data.data.length === size);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleSelect = (item: ConversationItem) => {
    // if (currentEditId === item.id) return;
    navigate(`/chat/${item.id}`).then(() => {});
  };

  const remove = (id: string) => {
    if (loading) {
      message.warning("当前会话正在加载消息，请稍后再试");
      return;
    }

    setConversations((conversations) => {
      return conversations.map((item) => {
        if (item.id === id) {
          return { ...item, pending: true };
        }
        return item;
      });
    });

    del(`/conversation/${id}`).then(() => {
      setConversations((conversations) => {
        return conversations.map((item) => {
          if (item.id === id) {
            return { ...item, deleted: true };
          }
          return item;
        });
      });
    });
  };

  return (
    <div className="pts:bg-white pts:h-full">
      <NavigationBar />
      <div
        id="cscs-agent-conversations-result-scrollable"
        className="pts:pt-[52px] pts:h-[calc(100vh-48px)] pts:overflow-y-auto mini-scrollbar"
      >
        <InfiniteScroll
          dataLength={conversations.length}
          next={() => handleSearch(keyword, agentCode)}
          hasMore={hasMore}
          loader={null}
          scrollableTarget="cscs-agent-conversations-result-scrollable"
          style={{ overflow: "hidden" }}
        >
          <div className="pts:flex pts:bg-white pts:mx-auto pts:border-[rgba(0,0,0,0.15)] pts:border-1 pts:rounded-sm pts:w-[752px] pts:h-10">
            <div className="pts:flex pts:items-center pts:border-[rgba(0,0,0,0.15)] pts:border-r-1 pts:min-w-[146px] pts:max-w-[146px]">
              <Select
                placeholder="选择智能体"
                variant="borderless"
                className="pts:w-full"
                options={agentOptions}
                value={agentCode}
                onChange={(value) => {
                  setAgentCode(value);
                  handleSearch(keyword, value, 1);
                }}
              />
            </div>
            <Input
              placeholder="请输入搜索关键字"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onPressEnter={() => {
                if (!searchOnChange) {
                  setSearchOnChange(true);
                }
                handleSearch(inputValue, agentCode, 1);
              }}
              allowClear
              className="pts:text-black-85"
              variant="borderless"
            />
          </div>
          <div className="pts:mx-auto pts:mt-[60px] pts:w-[800px]">
            {conversations.map((item) => (
              <div
                key={item.id}
                className={`pts:mb-2 pts:overflow-hidden pts:transform-all pts:duration-300 ${item.deleted ? "pts:h-0 pts:opacity-0" : "pts:h-auto pts:opacity-100"} ${item.pending ? "pts:animate-pulse" : ""}`}
              >
                <div
                  className="pts:hover:bg-[#F8F9FB] pts:px-6 pts:py-3 pts:cursor-pointer"
                  onClick={() => handleSelect(item)}
                >
                  <div className="pts:group pts:flex pts:justify-between pts:pb-2">
                    <div className="pts:flex pts:items-center pts:h-6 pts:font-medium pts:text-black-85 pts:text-md">
                      <AgentIcon code={item.current_agent_code} className="pts:mr-4" />
                      <Title
                        title={item.title}
                        keyword={keyword}
                        onEdited={(title) => {
                          rename(item.id, title);
                        }}
                        onCancel={() => {
                          setCurrentEditId(null);
                        }}
                        editable={currentEditId === item.id}
                      />
                      <div className="pts:hidden pts:group-hover:inline-block pts:ml-1">
                        <Button
                          type="text"
                          size="small"
                          icon={<Icon icon="Edit" className="pts:text-primary" />}
                          hidden={currentEditId === item.id}
                          onClick={(e) => {
                            e.stopPropagation();
                            setCurrentEditId(item.id);
                          }}
                        />
                      </div>
                      <div className="pts:hidden pts:group-hover:inline-block pts:ml-1">
                        <Button
                          type="text"
                          size="small"
                          icon={<Icon icon="Delete" className="pts:text-danger" />}
                          onClick={(e) => {
                            e.stopPropagation();
                            Modal.confirm({
                              title: <span className="pts:text-black-85">确定要删除会话吗？</span>,
                              content: <span className="pts:text-black-65">删除后，会话将不可恢复。</span>,
                              onOk() {
                                remove(item.id);
                              },
                              okButtonProps: {
                                danger: true,
                              },
                            });
                          }}
                        />
                      </div>
                    </div>
                    <div className="pts:text-black-65 pts:text-sm">{dayjs(item.updated_at).format("YYYY-MM-DD")}</div>
                  </div>
                  <div className="pts:text-black-65">
                    <Content keyword={keyword} content={item.content}></Content>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </InfiniteScroll>

        {conversations.length === 0 && <Empty className="pts:mx-auto pts:mt-10" />}

        <div className="pts:text-center">
          <Spin spinning={loading} indicator={<RedoOutlined spin />} size="small" />
        </div>
      </div>

      <div
        className="pts:right-[40px] pts:bottom-[160px] pts:z-10 pts:fixed pts:flex pts:justify-center pts:items-center pts:bg-[rgba(37,45,62,0.45)] pts:hover:bg-[rgba(37,45,62,0.65)] pts:rounded-[20px] pts:w-10 pts:h-10 pts:active:animate-ping pts:cursor-pointer"
        onClick={() => {
          document.getElementById("cscs-agent-conversations-result-scrollable")?.scrollTo({
            top: 0,
            behavior: "smooth",
          });
        }}
      >
        <Icon icon="BackTop" className="pts:text-white" />
      </div>
    </div>
  );
};

export default ConversationSearch;
