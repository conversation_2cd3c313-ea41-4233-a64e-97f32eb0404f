import { nanoid } from "nanoid";

import { RequestOptions, cancelRequest, request } from "@/request";

export class Transmitter {
  url = "/chat/completion";
  requestId;
  headers: Record<string, unknown> = {};
  method = "POST";
  timeout = 0;

  constructor(
    url?: string,
    options?: {
      headers?: Record<string, unknown>;
      method?: string;
      timeout?: number;
    },
  ) {
    if (url) {
      this.url = url;
    }
    this.requestId = nanoid();
    if (options) {
      this.headers = options.headers ?? {};
      this.method = options.method ?? "POST";
      this.timeout = options.timeout ?? 0;
    }
  }

  public send(params: {
    message: string;
    agentCode: string;
    conversationId?: string;
    extendParams?: Record<string, unknown>;
  }) {
    const { message, agentCode, conversationId, extendParams = {} } = params;
    const options = this.getOptions();

    options.data = {
      conversation_id: conversationId,
      message,
      agent_code: agentCode,
      extend_params: extendParams,
    };

    return request<ReadableStream>(options);
  }

  public cancel() {
    cancelRequest(this.requestId);
  }

  private getOptions() {
    const options: RequestOptions = {
      url: this.url,
      requestId: this.requestId,
      headers: {
        "Content-Type": "application/json",
        Accept: "text/event-stream",
        ...this.headers,
      },
      responseType: "stream",
      method: this.method,
      adapter: "fetch",
      timeout: this.timeout,
    };

    return options;
  }
}
