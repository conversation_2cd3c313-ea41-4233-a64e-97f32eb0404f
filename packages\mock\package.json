{"name": "@cscs-agent/mock", "version": "0.4.1", "description": "Mock tools for Agent project", "type": "module", "files": ["lib", "README.md"], "main": "lib/index.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.js --fix"}, "dependencies": {}, "devDependencies": {"@types/node": "^22.15.0", "eslint": "^9.25.1", "jsdom": "^26.1.0", "typescript": "^5.8.2"}, "keywords": ["cscs-agent"], "author": "CSCS Team", "license": "ISC", "packageManager": "pnpm@10.9.0", "engines": {"node": ">=18.0.0"}}