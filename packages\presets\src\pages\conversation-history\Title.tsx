import { Input, Modal } from "antd";
import React, { useEffect } from "react";

import Highlight from "./Highlight";

interface ConversationTitleProps {
  title: string;
  keyword: string;
  onEdited: (title: string) => void;
  editable: boolean;
  onCancel: () => void;
}

const Title: React.FC<ConversationTitleProps> = (props) => {
  const { title, keyword, onEdited, onCancel, editable } = props;
  const [value, setValue] = React.useState(title);

  useEffect(() => {
    if (editable) {
      setValue(title);
    }
  }, [editable, title]);

  const handleOk = () => {
    onEdited(value);
  };

  if (!editable) {
    return (
      <span className="pts:font-bold pts:text-base">
        <Highlight text={title} keyword={keyword} />
      </span>
    );
  }

  return (
    <span
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Modal title="重命名对话" onOk={handleOk} open={editable} onCancel={onCancel}>
        <Input value={value} onChange={(e) => setValue(e.target.value)} placeholder="请输入" />
      </Modal>
    </span>
  );
};

export default Title;
