import { Form, Input, Modal } from "antd";
import React, { PropsWith<PERSON>hildren, useEffect, useState } from "react";

import { StandardResponse, post, put, useActiveAgentCode } from "@cscs-agent/core";

const AddUpdateUserPrompt: React.FC<
  PropsWithChildren<{
    onSuccess?: () => void;
    id?: number;
    data?: Record<string, any>;
  }>
> = (props) => {
  const { children, onSuccess, id, data } = props;
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [activeAgentCode] = useActiveAgentCode();

  useEffect(() => {
    if (data && open) {
      form.setFieldsValue(data);
    }
  }, [data, open]);

  const handleOpen = (e: React.MouseEvent) => {
    setOpen(true);
    e.stopPropagation();
  };

  const handleClose = () => {
    setOpen(false);
    form.resetFields();
  };

  const handleOk = () => {
    form.validateFields().then((values) => {
      const _values = {
        ...values,
        agent_code: activeAgentCode,
      };

      if (id) {
        put<StandardResponse>("/user-prompt-template/" + id, _values).then((res) => {
          if (res.data.code === 200) {
            handleClose();
            if (onSuccess) {
              onSuccess();
            }
          }
        });
      } else {
        post<StandardResponse>("/user-prompt-template", _values).then((res) => {
          if (res.data.code === 200) {
            handleClose();
            if (onSuccess) {
              onSuccess();
            }
          }
        });
      }
    });
  };

  return (
    <>
      <span onClick={handleOpen} onMouseDown={(e) => e.stopPropagation()}>
        {children}
      </span>
      <div onClick={(e) => e.stopPropagation()} hidden>
        <Modal open={open} onCancel={handleClose} onOk={handleOk} title={(id ? "编辑" : "新建") + "自定义模板"}>
          <div className="pts:pt-4">
            <Form form={form} labelCol={{ span: 4 }}>
              <Form.Item
                label="模板标题"
                name="title"
                rules={[
                  {
                    required: true,
                    message: "请输入模板标题",
                  },
                  {
                    max: 15,
                    message: "最多15个字",
                  },
                ]}
              >
                <Input placeholder="请输入最多20个字" />
              </Form.Item>
              <Form.Item
                label="模板描述"
                name="description"
                rules={[
                  {
                    max: 20,
                    message: "最多20个字",
                  },
                ]}
              >
                <Input placeholder="请输入最多20个字" />
              </Form.Item>
              <Form.Item
                label="模板内容"
                name="prompt"
                rules={[
                  {
                    required: true,
                    message: "请输入模板内容",
                  },
                  {
                    max: 1000,
                    message: "最多1000个字",
                  },
                ]}
              >
                <Input.TextArea placeholder="请输入提示词内容，最多1000字，如帮我写xx企业预警报告" />
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </>
  );
};

export default AddUpdateUserPrompt;
