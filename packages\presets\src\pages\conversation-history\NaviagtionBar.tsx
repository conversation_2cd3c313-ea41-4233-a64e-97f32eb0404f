import { <PERSON><PERSON> } from "antd";
import React from "react";

import { PresetsCommand } from "@/command";
import { DefaultAgentLayoutContext } from "@/layout/Basic";
import { useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const NavigationBar: React.FC = () => {
  const { sideBarOpen } = React.useContext(DefaultAgentLayoutContext);
  const runner = useCommandRunner();

  const openSideBar = () => {
    runner(PresetsCommand.OpenSideBar);
  };

  return (
    <div className="pts:px-6 pts:py-3 pts:text-[rgba(37,45,62,0.85)]">
      {!sideBarOpen && (
        <Button icon={<Icon icon="SideBar" />} type="text" size="small" onClick={openSideBar} className="pts:mr-4" />
      )}
      <span className="pts:font-bold">历史会话</span>
    </div>
  );
};

export default NavigationBar;
