/**
 * Upgrade command implementation
 * Upgrades an agent project to match the latest template
 */

import { existsSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

import chalk from "chalk";
import inquirer from "inquirer";
import ora from "ora";

import type {
  CompareFileItem,
  StreamlinedUpgradeResult,
  UpgradeCommandOptions,
  ValidationResult,
} from "../../types.js";
import { ConditionEvaluator } from "../../utils/condition-evaluator.js";
import { DependencyUpdater } from "../../utils/dependency-updater.js";
import { FileSystemManager } from "../../utils/filesystem-manager.js";
import { Logger } from "../../utils/logger.js";
import { StreamlinedFileUpgrader } from "../../utils/streamlined-file-upgrader.js";
import { getFilesToUpgrade } from "./files.js";

// Define core files to compare with contextual prompts for LLM analysis

export class UpgradeCommand {
  private streamlinedUpgrader: StreamlinedFileUpgrader;
  private dependencyUpdater: DependencyUpdater;
  private fsManager: FileSystemManager;
  private conditionEvaluator: ConditionEvaluator;

  constructor(rootDir: string) {
    // Get template path
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    const templatePath = join(rootDir, "templates", "basic");

    this.streamlinedUpgrader = new StreamlinedFileUpgrader(templatePath);
    this.dependencyUpdater = new DependencyUpdater();
    this.fsManager = new FileSystemManager();
    this.conditionEvaluator = new ConditionEvaluator();
  }

  /**
   * Discover and read files for comparison with condition evaluation
   */
  private async discoverProjectFiles(targetPath: string): Promise<CompareFileItem[]> {
    const filesToCompare: CompareFileItem[] = [];

    const filesToUpgrade = await getFilesToUpgrade();

    for (const fileConfig of filesToUpgrade) {
      const fullPath = join(targetPath, fileConfig.path);

      // Evaluate condition if present
      if (fileConfig.condition) {
        Logger.info(`Evaluating condition for ${fileConfig.path}: ${fileConfig.condition}`);

        try {
          await this.conditionEvaluator.init();
          const evaluationResult = await this.conditionEvaluator.evaluateCondition(fileConfig.condition);

          Logger.info(`Condition evaluation result: ${evaluationResult.satisfied ? "satisfied" : "not satisfied"}`);
          Logger.info(`Reasoning: ${evaluationResult.reasoning}`);

          if (!evaluationResult.satisfied) {
            Logger.info(`Skipping ${fileConfig.path} - condition not satisfied`);
            continue;
          }
        } catch (error) {
          Logger.warning(
            `Condition evaluation failed for ${fileConfig.path}: ${error instanceof Error ? error.message : "Unknown error"}. Proceeding with file processing.`,
          );
          // Continue processing the file if condition evaluation fails
        }
      }

      if (existsSync(fullPath)) {
        try {
          const content = await this.fsManager.readFile(fullPath);
          filesToCompare.push({
            filePath: fullPath,
            content,
            prompt: fileConfig.prompt,
            removed: fileConfig.removed,
          });
        } catch (error) {
          Logger.warning(
            `Failed to read file ${fullPath}: ${error instanceof Error ? error.message : "Unknown error"}`,
          );
        }
      } else {
        if (!fileConfig.removed) {
          // For conditional files, only create if condition is satisfied
          if (fileConfig.condition) {
            Logger.info(`Creating new file ${fullPath} - condition satisfied`);
          }

          // create blank file
          await this.fsManager.writeFile(fullPath, "");
          filesToCompare.push({
            filePath: fullPath,
            content: "",
            prompt: fileConfig.prompt,
          });
        }
      }
    }

    return filesToCompare;
  }

  /**
   * Validate target path is a valid agent project
   */
  private validateTargetPath(targetPath: string): ValidationResult {
    if (!existsSync(targetPath)) {
      return {
        valid: false,
        error: `Target path does not exist: ${targetPath}`,
      };
    }

    const packageJsonPath = join(targetPath, "package.json");
    if (!existsSync(packageJsonPath)) {
      return {
        valid: false,
        error: "No package.json found. This doesn't appear to be a Node.js project.",
      };
    }

    const srcPath = join(targetPath, "src");
    if (!existsSync(srcPath)) {
      return {
        valid: false,
        error: "No src directory found. This doesn't appear to be an agent project.",
      };
    }

    return { valid: true };
  }

  /**
   * Interactive mode for upgrade
   */
  private async runInteractive(): Promise<void> {
    Logger.title("CSCS Agent Project Upgrader powered by AI.");

    // Get target path
    const { targetPath } = await inquirer.prompt([
      {
        type: "input",
        name: "targetPath",
        message: "Target project path:",
        default: process.cwd(),
        validate: (input: string) => {
          const validation = this.validateTargetPath(input);
          return validation.valid || validation.error || "Invalid target path";
        },
      },
    ]);

    // Get upgrade options
    const options = await inquirer.prompt([
      {
        type: "confirm",
        name: "dryRun",
        message: "Run in dry-run mode (preview changes without applying)?",
        default: false,
      },
      {
        type: "confirm",
        name: "skipBackup",
        message: "Skip creating backup files?",
        default: false,
      },
      {
        type: "confirm",
        name: "force",
        message: "Force upgrade even if there are warnings?",
        default: false,
      },
    ]);

    // Confirm upgrade
    const { confirm } = await inquirer.prompt([
      {
        type: "confirm",
        name: "confirm",
        message: `Upgrade project at '${chalk.cyan(targetPath)}'?`,
        default: true,
      },
    ]);

    if (!confirm) {
      Logger.info("Upgrade cancelled");
      return;
    }

    // Execute upgrade
    const success = await this.executeUpgrade({
      targetPath,
      interactive: true,
      ...options,
    });

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Non-interactive mode for upgrade
   */
  private async runNonInteractive(options: UpgradeCommandOptions): Promise<void> {
    const targetPath = options.targetPath || process.cwd();

    // Validate target path
    const validation = this.validateTargetPath(targetPath);
    if (!validation.valid) {
      Logger.error(validation.error || "Invalid target path");
      process.exit(1);
    }

    // Execute upgrade
    const success = await this.executeUpgrade({
      ...options,
      targetPath,
    });

    if (!success) {
      process.exit(1);
    }
  }

  /**
   * Execute the streamlined upgrade process
   */
  private async executeUpgrade(options: UpgradeCommandOptions): Promise<boolean> {
    const targetPath = options.targetPath!;

    try {
      Logger.info(`Starting upgrade for project: ${chalk.cyan(targetPath)}`);

      // Phase 1: File Discovery
      const spinner = ora({
        text: chalk.cyan("Discovering project files..."),
        spinner: "dots2",
        interval: 120,
      }).start();
      const projectFiles = await this.discoverProjectFiles(targetPath);

      if (projectFiles.length === 0) {
        spinner.fail("File discovery failed");
        Logger.error("No relevant files found in the project");
        return false;
      }

      // Basic validation - check if package.json exists
      const hasPackageJson = projectFiles.some((file) => file.filePath.endsWith("package.json"));
      if (!hasPackageJson) {
        spinner.fail("Project validation failed");
        Logger.error("This doesn't appear to be a valid Node.js project (no package.json found)");
        return false;
      }

      spinner.succeed(`File discovery completed (${projectFiles.length} files found)`);

      // Phase 2: Streamlined Upgrade (combines comparison and modification)
      spinner.start(chalk.cyan("Analyzing and upgrading files..."));
      const upgradeResults = await this.streamlinedUpgrader.upgradeFiles(projectFiles);
      spinner.succeed("File analysis and upgrade completed");

      // Filter results that require updates
      const filesToUpdate = upgradeResults.filter((result) => result.requiresUpdate);

      if (filesToUpdate.length === 0) {
        Logger.info("No file updates required. Your project is already up to date!");
        return true;
      }

      // Show upgrade summary
      this.displayUpgradeSummary(filesToUpdate);

      // Check if dry run
      if (options.dryRun) {
        Logger.info("Dry run completed. No changes were made.");
        return true;
      }

      // Process each file that needs updates
      for (const result of filesToUpdate) {
        // Display diff for this file
        this.streamlinedUpgrader.displayDiff(result.diffResult);

        // Get user confirmation if interactive
        if (options.interactive && !options.force) {
          const userChoice = await this.streamlinedUpgrader.promptUserForConfirmation(result.diffResult);

          if (userChoice === "view") {
            // Show full diff again and re-prompt
            this.streamlinedUpgrader.displayDiff(result.diffResult);
            const retryChoice = await this.streamlinedUpgrader.promptUserForConfirmation(result.diffResult);
            if (retryChoice !== "confirm") {
              Logger.info(`Skipped: ${result.filePath}`);
              continue;
            }
          } else if (userChoice === "revert") {
            Logger.info(`Skipped: ${result.filePath}`);
            continue;
          }
        }

        // Apply the upgrade
        await this.streamlinedUpgrader.applyUpgrade(targetPath, result, !options.skipBackup);
      }

      // Phase 3: Handle dependency updates from package.json changes
      const packageJsonResult = filesToUpdate.find((result) => result.filePath === "package.json");
      if (packageJsonResult) {
        spinner.start(chalk.cyan("Processing dependency updates..."));
        await this.processDependencyUpdates(targetPath, packageJsonResult);
        spinner.succeed("Dependency updates processed");
      }

      Logger.success("Upgrade completed successfully!");
      this.showStreamlinedSuccessMessage(filesToUpdate);

      return true;
    } catch (error) {
      Logger.error(`Upgrade failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      return false;
    }
  }

  /**
   * Display streamlined upgrade summary
   */
  private displayUpgradeSummary(results: StreamlinedUpgradeResult[]): void {
    Logger.info("\n" + chalk.bold("Upgrade Summary:"));

    Logger.info(`  Total files analyzed: ${results.length}`);
    Logger.info(`  Files requiring updates: ${results.filter((r) => r.requiresUpdate).length}`);

    const criticalUpdates = results.filter((r) => r.updatePriority === "critical").length;
    const recommendedUpdates = results.filter((r) => r.updatePriority === "recommended").length;
    const optionalUpdates = results.filter((r) => r.updatePriority === "optional").length;

    if (criticalUpdates > 0) {
      Logger.warning(`  Critical updates: ${criticalUpdates}`);
    }
    if (recommendedUpdates > 0) {
      Logger.info(`  Recommended updates: ${recommendedUpdates}`);
    }
    if (optionalUpdates > 0) {
      Logger.info(`  Optional updates: ${optionalUpdates}`);
    }

    Logger.info("\nFiles to be updated:");
    results
      .filter((r) => r.requiresUpdate)
      .forEach((result) => {
        const priorityColor =
          result.updatePriority === "critical"
            ? chalk.red
            : result.updatePriority === "recommended"
              ? chalk.yellow
              : chalk.gray;
        Logger.info(`  ${priorityColor("●")} ${result.filePath} (${result.updatePriority})`);
      });
  }

  /**
   * Show streamlined success message
   */
  private showStreamlinedSuccessMessage(results: StreamlinedUpgradeResult[]): void {
    Logger.info("\n" + chalk.green("Upgrade completed successfully!"));
    Logger.info("\nNext steps:");
    Logger.info("  1. Review the changes made to your project");
    Logger.info("  2. Test your application to ensure everything works");
    Logger.info("  3. Commit your changes to version control");

    const updatedFiles = results.filter((r) => r.requiresUpdate);
    if (updatedFiles.length > 0) {
      Logger.info(`\nUpdated ${updatedFiles.length} file(s):`);
      updatedFiles.forEach((result) => {
        Logger.info(`  - ${result.filePath}`);
      });
    }
  }

  /**
   * Process dependency updates from package.json changes
   */
  private async processDependencyUpdates(
    targetPath: string,
    packageJsonResult: StreamlinedUpgradeResult,
  ): Promise<void> {
    try {
      // Parse original and modified package.json to extract dependency changes
      const originalPackage = JSON.parse(packageJsonResult.originalContent);
      const modifiedPackage = JSON.parse(packageJsonResult.modifiedContent);

      const dependencyUpdates = this.dependencyUpdater.generateDependencyUpdates(
        originalPackage.dependencies || {},
        originalPackage.devDependencies || {},
        modifiedPackage.dependencies || {},
        modifiedPackage.devDependencies || {},
      );

      if (dependencyUpdates.length > 0) {
        Logger.info(`Processing ${dependencyUpdates.length} dependency updates...`);
        await this.dependencyUpdater.updateDependencies(targetPath, dependencyUpdates);
      }
    } catch (error) {
      Logger.warning(
        `Failed to process dependency updates: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Execute upgrade command
   */
  async execute(options: UpgradeCommandOptions = {}): Promise<void> {
    try {
      if (options.interactive) {
        await this.runInteractive();
      } else {
        await this.runNonInteractive(options);
      }
    } catch (error) {
      Logger.error(`Failed to upgrade project: ${error instanceof Error ? error.message : "Unknown error"}`);
      process.exit(1);
    }
  }
}
