import { Al<PERSON>, Flex, Space } from "antd";
import React, { useState } from "react";

import { Message } from "@/core/common/message";
import { useActiveAgentConfig } from "@/core/hooks/useAgent";
import { MessageStatus, Role } from "@/types";

import { MessageRender } from "../render";
import MessageContext from "./Context";

interface BubbleProps {
  message: Message;
  messageRender?: (content: string) => React.ReactNode;
  avatar: {
    icon: React.ReactNode;
    style?: React.CSSProperties;
  };
  placement: "start" | "end";
  role: Role;
}

const Bubble: React.FC<BubbleProps> = (props) => {
  const { message, placement, role } = props;
  const [messageState, setMessageState] = useState<Record<string, any>>({});

  return (
    <MessageContext.Provider value={{ message, messageState, setMessageState }}>
      <Flex align="start" justify={placement === "end" ? "flex-end" : "flex-start"} className="ag:w-full">
        <div className={`${placement === "end" ? "ag:max-w-[80%]" : "ag:w-[80%]"} cscs-agent-bubble`}>
          <div className={`ag:py-2 ${placement === "end" ? "ag:bg-[rgba(37,45,62,0.06)] ag:p-3 ag:rounded-lg" : ""}`}>
            <Header />
            <MessageRender data={message} />
            {message.status === MessageStatus.Loading && <div className="ag:mt-4 ag:message-loader"></div>}
          </div>

          <div
            className={`ag:flex ${role === Role.HUMAN ? "ag:mt-2 ag:flex-row-reverse cscs-agent-bubble-footer-human" : ""}`}
          >
            {message.status === MessageStatus.Finished ? <Footer role={role} /> : null}
          </div>
          <div className="ag:mt-2 ag:max">
            {message.error && <Alert type="error" message={message.getErrorMessage()} showIcon />}
            {message.status === MessageStatus.Cancelled && (
              <span className="ag:text-black-45 ag:text-sm">已停止生成</span>
            )}
          </div>
        </div>
      </Flex>
    </MessageContext.Provider>
  );
};

const Header: React.FC = () => {
  const agentConfig = useActiveAgentConfig();

  return (
    <Space>
      {agentConfig?.message?.slots?.header?.widgets?.map((Widget) => (
        <Widget.component key={Widget.code} {...Widget.props} />
      ))}
    </Space>
  );
};

const Footer: React.FC<{ role: Role }> = (props) => {
  const { role } = props;
  const agentConfig = useActiveAgentConfig();

  const widgets = agentConfig?.message?.slots?.footer?.widgets ?? [];

  if (widgets.length === 0) return null;

  return (
    <Space className="ag:text-black-65">
      {widgets.map((Widget) => {
        if (Widget.role === role) {
          return <Widget.component key={Widget.code} {...Widget.props} />;
        }
      })}
    </Space>
  );
};

export default Bubble;
