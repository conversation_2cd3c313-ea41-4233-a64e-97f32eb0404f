import { join } from "path";

import { CoreFileConfig } from "../../types.js";
import { FileSystemManager } from "../../utils/filesystem-manager.js";

export async function getFilesToUpgrade() {
  const fsManager = new FileSystemManager();

  let viteFileContent = "";

  try {
    viteFileContent = await fsManager.readFile(join(process.cwd(), "vite.config.js"));
  } catch {
    console.info("vite.config.js removed");
  }

  const files: CoreFileConfig[] = [
    {
      path: "package.json",
      prompt:
        "This is the main package.json file containing project metadata, dependencies, and scripts. When analyzing changes, focus on: dependency version updates(particularly @cscs-agent/core, @cscs-agent/presets and @cscs-agent/icons), new dependencies that enhance functionality, script modifications for build/dev processes, and metadata updates. Be cautious with breaking changes in major version updates and ensure compatibility between dependencies.",
    },
    {
      path: "src/main.tsx",
      prompt:
        "Import agent-config to here and use initApp() instead of createRoot(). Focus on the changes in usage of initApp(), every params is necessary in most cases.",
    },
    {
      path: "src/agent-config.tsx",
      prompt:
        "This is the agent configuration. Import the example widget `MyButton` if there aren't import any `widget` from `./widgets` in older file. When analyzing changes, focus on: agent-specific settings, form handling improvements, and validation logic. Ensure configuration schema compatibility and proper user experience for agent setup. ",
    },
    {
      path: "src/pages/home/<USER>",
      prompt: "Only use DefaultBasicLayout here.",
    },
    {
      path: "mock/api.mock.ts",
      prompt: "This is API mock config",
    },
    {
      path: "config/proxy.ts",
      prompt: `
      Analysis following vite.config file content:
      \`\`\`js
      ${viteFileContent}
      \`\`\`
      Find out the proxy configures in it, generate upgraded file content refer to this configures and template file.
      `,
    },
    {
      path: "vite.config.js",
      prompt: "This file had been removed in latest version, you can remove this file from project.",
      removed: true,
    },
    {
      path: "src/widgets/button/index.tsx",
      prompt: "This is an template file",
      condition: `The file ${join(process.cwd(), "src/agent-config.tsx")} and doesn't import any widgets`,
    },
  ];
  return files;
}
