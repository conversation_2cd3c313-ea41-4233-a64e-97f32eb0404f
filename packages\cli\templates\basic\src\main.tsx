import "@@/@cscs-agent/core/dist/agent-tailwind.css";
import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
import "@@/@cscs-agent/icons/dist/icons.css";
import "virtual:app-dep-info";

import "./styles.css";

import dayjs from "dayjs";

import { RouterConfig, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, ConversationHistory, Login, defaultAuthGuard } from "@cscs-agent/presets";

import { config } from "./agent-config";
import Home from "./pages/home";

dayjs.locale("zh-cn");

const routerConfig: RouterConfig = {
  pages: {
    home: {
      Component: Home,
    },
    chat: {
      Component: Chat,
    },
    agentHome: {
      Component: AgentHome,
    },
    login: {
      enable: true,
      Component: Login,
    },
    conversationHistory: {
      Component: ConversationHistory,
    },
  },
  authGuard: defaultAuthGuard,
  rootRoutes: [],
};

initApp({
  loginUrl: "/login",
  routerConfig,
  agentChatConfig: config,
}).then(() => {
  console.log("App initialized successfully");
});
